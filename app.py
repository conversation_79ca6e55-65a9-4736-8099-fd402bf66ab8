# Student Placement Predictor - Streamlit Web Application
# 
# Instructions to run:
# 1. Open terminal in VS Code (Terminal -> New Terminal)
# 2. Install requirements: pip install -r requirements.txt
# 3. Run data generation first: python generate_data.py
# 4. Run the Streamlit app: streamlit run app.py
# 5. Open the displayed URL in your browser (usually http://localhost:8501)

import streamlit as st
import pandas as pd
import numpy as np
import pickle
import sqlite3
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go

# Configure Streamlit page
st.set_page_config(
    page_title="Student Placement Predictor",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
def load_css():
    """Load custom CSS styling"""
    st.markdown("""
    <style>
    /* Main title styling */
    .main-title {
        font-size: 3rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    /* Subtitle styling */
    .subtitle {
        font-size: 1.2rem;
        color: #666;
        text-align: center;
        margin-bottom: 3rem;
    }
    
    /* Prediction result cards */
    .result-card {
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        margin: 1rem 0;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .eligible-card {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }
    
    .not-eligible-card {
        background: linear-gradient(135deg, #dc3545, #fd7e14);
        color: white;
    }
    
    /* Custom button styling */
    .stButton > button {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-size: 1.1rem;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,123,255,0.4);
    }
    
    /* Info boxes */
    .info-box {
        padding: 1rem;
        border-radius: 10px;
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
        margin: 1rem 0;
    }
    
    /* Stats container */
    .stats-container {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        padding: 1.5rem;
        border-radius: 15px;
        margin: 1rem 0;
    }
    </style>
    """, unsafe_allow_html=True)

@st.cache_resource
def load_model():
    """Load the trained placement prediction model"""
    try:
        with open('placement_model.pkl', 'rb') as file:
            model = pickle.load(file)
        
        with open('model_info.pkl', 'rb') as file:
            model_info = pickle.load(file)
            
        return model, model_info
    except FileNotFoundError:
        st.error("❌ Model files not found! Please run 'python generate_data.py' first.")
        st.stop()

def save_prediction_to_db(name, roll_number, cgpa, skills, internships, 
                         certifications, projects, prediction, probability):
    """Save student prediction to SQLite database"""
    try:
        conn = sqlite3.connect('student_predictions.db')
        cursor = conn.cursor()
        
        skills_str = ', '.join(skills)
        
        cursor.execute('''
            INSERT INTO predictions (name, roll_number, cgpa, skills, internships, 
                                   certifications, projects, prediction, probability)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, roll_number, cgpa, skills_str, internships, 
              certifications, projects, prediction, probability))
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        st.error(f"Error saving to database: {e}")
        return False

def get_prediction_history():
    """Retrieve prediction history from database"""
    try:
        conn = sqlite3.connect('student_predictions.db')
        df = pd.read_sql_query('''
            SELECT * FROM predictions 
            ORDER BY timestamp DESC 
            LIMIT 10
        ''', conn)
        conn.close()
        return df
    except Exception as e:
        st.error(f"Error reading from database: {e}")
        return pd.DataFrame()

def prepare_features(cgpa, skills, internships, certifications, projects):
    """Prepare features for model prediction"""
    skill_mapping = {
        'Python': 'python_skill',
        'Java': 'java_skill', 
        'C++': 'cpp_skill',
        'Data Science': 'data_science_skill',
        'AI/ML': 'ai_ml_skill',
        'SQL': 'sql_skill'
    }
    
    features = {
        'cgpa': cgpa,
        'python_skill': 1 if 'Python' in skills else 0,
        'java_skill': 1 if 'Java' in skills else 0,
        'cpp_skill': 1 if 'C++' in skills else 0,
        'data_science_skill': 1 if 'Data Science' in skills else 0,
        'ai_ml_skill': 1 if 'AI/ML' in skills else 0,
        'sql_skill': 1 if 'SQL' in skills else 0,
        'internships': internships,
        'certifications': 1 if certifications else 0,
        'projects': projects
    }
    
    return list(features.values())

def display_prediction_result(prediction, probability):
    """Display prediction result with styled cards"""
    if prediction == 1:
        st.markdown(f"""
        <div class="result-card eligible-card">
            <h2>🎉 Eligible for Placement!</h2>
            <h3>Confidence: {probability:.1%}</h3>
            <p>Great job! Your profile looks strong for campus placements.</p>
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown(f"""
        <div class="result-card not-eligible-card">
            <h2>📈 Need Improvement</h2>
            <h3>Confidence: {(1-probability):.1%}</h3>
            <p>Consider enhancing your skills, CGPA, or gaining more experience.</p>
        </div>
        """, unsafe_allow_html=True)

def main():
    """Main Streamlit application"""
    
    # Load CSS styling
    load_css()
    
    # Load ML model
    model, model_info = load_model()
    
    # App header
    st.markdown('<h1 class="main-title">🎓 Student Placement Predictor</h1>', 
                unsafe_allow_html=True)
    st.markdown('<p class="subtitle">Predict your placement eligibility using AI</p>', 
                unsafe_allow_html=True)
    
    # Sidebar for model information
    with st.sidebar:
        st.header("📊 Model Information")
        st.markdown(f"""
        <div class="stats-container">
            <h4>Model Performance</h4>
            <p><strong>Training Accuracy:</strong> {model_info['train_accuracy']:.1%}</p>
            <p><strong>Testing Accuracy:</strong> {model_info['test_accuracy']:.1%}</p>
            <p><strong>Algorithm:</strong> Random Forest</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.header("📋 Instructions")
        st.markdown("""
        1. Fill in your academic details
        2. Select your technical skills
        3. Enter your experience details
        4. Click 'Predict Placement' button
        5. View your prediction result
        """)
    
    # Main input form
    st.header("📝 Enter Your Details")
    
    # Create two columns for better layout
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Personal Information")
        name = st.text_input("👤 Full Name", placeholder="Enter your full name")
        roll_number = st.text_input("🆔 Roll Number", placeholder="Enter your roll number")
        cgpa = st.slider("📊 CGPA", min_value=5.0, max_value=10.0, value=7.5, step=0.1)
    
    with col2:
        st.subheader("Experience & Achievements")
        internships = st.number_input("💼 Number of Internships", min_value=0, max_value=10, value=0)
        certifications = st.radio("📜 Have Certifications?", ["Yes", "No"])
        projects = st.number_input("🚀 Number of Projects", min_value=0, max_value=20, value=0)
    
    # Skills selection
    st.subheader("💻 Technical Skills")
    skills_options = ['Python', 'Java', 'C++', 'Data Science', 'AI/ML', 'SQL', 
                     'Web Development', 'Mobile Development', 'DevOps']
    skills = st.multiselect("Select your technical skills:", skills_options)
    
    # Prediction button
    st.markdown("<br>", unsafe_allow_html=True)
    predict_col1, predict_col2, predict_col3 = st.columns([1, 2, 1])
    
    with predict_col2:
        predict_button = st.button("🔮 Predict Placement", use_container_width=True)
    
    # Handle prediction
    if predict_button:
        if not name or not roll_number:
            st.error("❌ Please fill in your name and roll number!")
        else:
            # Prepare features for prediction
            features = prepare_features(cgpa, skills, internships, 
                                      certifications == "Yes", projects)
            
            # Make prediction
            prediction = model.predict([features])[0]
            probability = model.predict_proba([features])[0][1]  # Probability of being placed
            
            # Display result
            st.markdown("<br>", unsafe_allow_html=True)
            display_prediction_result(prediction, probability)
            
            # Show probability chart
            st.subheader("📈 Prediction Probability")
            prob_data = pd.DataFrame({
                'Outcome': ['Not Placed', 'Placed'],
                'Probability': [1-probability, probability]
            })
            
            fig = px.bar(prob_data, x='Outcome', y='Probability', 
                        color='Outcome', 
                        color_discrete_map={
                            'Placed': '#28a745',
                            'Not Placed': '#dc3545'
                        })
            fig.update_layout(showlegend=False, height=400)
            st.plotly_chart(fig, use_container_width=True)
            
            # Save to database
            if save_prediction_to_db(name, roll_number, cgpa, skills, internships,
                                   certifications == "Yes", projects, prediction, probability):
                st.success("✅ Prediction saved to database!")
            
            # Show recommendations
            st.subheader("💡 Recommendations")
            if prediction == 0:
                recommendations = []
                if cgpa < 7.0:
                    recommendations.append("🔹 Focus on improving your CGPA (current: {:.1f})".format(cgpa))
                if internships < 2:
                    recommendations.append("🔹 Try to get more internships (current: {})".format(internships))
                if len(skills) < 3:
                    recommendations.append("🔹 Learn more technical skills (current: {})".format(len(skills)))
                if projects < 3:
                    recommendations.append("🔹 Work on more projects (current: {})".format(projects))
                if certifications == "No":
                    recommendations.append("🔹 Consider getting relevant certifications")
                
                for rec in recommendations:
                    st.markdown(rec)
            else:
                st.success("🌟 Great profile! Keep up the excellent work!")
    
    # Display recent predictions history
    st.header("📚 Recent Predictions")
    history_df = get_prediction_history()
    
    if not history_df.empty:
        # Display as a formatted table
        display_df = history_df[['name', 'roll_number', 'cgpa', 'prediction', 
                                'probability', 'timestamp']].copy()
        display_df['prediction'] = display_df['prediction'].apply(
            lambda x: "✅ Eligible" if x == 1 else "❌ Not Eligible"
        )
        display_df['probability'] = display_df['probability'].apply(
            lambda x: f"{x:.1%}"
        )
        display_df.columns = ['Name', 'Roll Number', 'CGPA', 'Prediction', 
                             'Confidence', 'Timestamp']
        
        st.dataframe(display_df, use_container_width=True)
    else:
        st.info("No prediction history available yet.")
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666; padding: 20px;'>
        <p>🎓 Student Placement Predictor | Built with Streamlit & Machine Learning</p>
        <p>💡 This tool provides predictions based on historical data and should be used as guidance only.</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()