# Student Placement Predictor - Data Generation and Model Training
# 
# Instructions to run:
# 1. Open terminal in VS Code (Terminal -> New Terminal)
# 2. Install requirements: pip install -r requirements.txt
# 3. Run this script first to generate data and train model: python generate_data.py
# 4. Then run the Streamlit app: streamlit run app.py
# 5. Open the displayed URL in your browser

import pandas as pd
import numpy as np
import pickle
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score, classification_report
import sqlite3

def create_dummy_data():
    """Generate dummy placement data for training the ML model"""
    print("Generating dummy placement data...")
    
    np.random.seed(42)
    n_samples = 1000
    
    # Generate random student data
    data = {
        'cgpa': np.random.normal(7.5, 1.2, n_samples),  # Normal distribution around 7.5
        'python_skill': np.random.choice([0, 1], n_samples, p=[0.4, 0.6]),
        'java_skill': np.random.choice([0, 1], n_samples, p=[0.5, 0.5]),
        'cpp_skill': np.random.choice([0, 1], n_samples, p=[0.6, 0.4]),
        'data_science_skill': np.random.choice([0, 1], n_samples, p=[0.7, 0.3]),
        'ai_ml_skill': np.random.choice([0, 1], n_samples, p=[0.8, 0.2]),
        'sql_skill': np.random.choice([0, 1], n_samples, p=[0.5, 0.5]),
        'internships': np.random.poisson(1.5, n_samples),  # Poisson distribution
        'certifications': np.random.choice([0, 1], n_samples, p=[0.3, 0.7]),
        'projects': np.random.poisson(2, n_samples)  # Poisson distribution
    }
    
    # Clip values to realistic ranges
    data['cgpa'] = np.clip(data['cgpa'], 5.0, 10.0)
    data['internships'] = np.clip(data['internships'], 0, 5)
    data['projects'] = np.clip(data['projects'], 0, 8)
    
    # Create placement target based on logical rules
    placement = []
    for i in range(n_samples):
        score = 0
        
        # CGPA contribution (most important factor)
        if data['cgpa'][i] >= 8.5:
            score += 4
        elif data['cgpa'][i] >= 7.5:
            score += 3
        elif data['cgpa'][i] >= 6.5:
            score += 2
        else:
            score += 1
            
        # Skills contribution
        score += data['python_skill'][i] * 0.8
        score += data['java_skill'][i] * 0.7
        score += data['cpp_skill'][i] * 0.6
        score += data['data_science_skill'][i] * 1.2
        score += data['ai_ml_skill'][i] * 1.5
        score += data['sql_skill'][i] * 0.8
        
        # Experience contribution
        score += min(data['internships'][i] * 0.8, 3)  # Cap internship contribution
        score += data['certifications'][i] * 0.7
        score += min(data['projects'][i] * 0.4, 2)  # Cap projects contribution
        
        # Add some randomness
        score += np.random.normal(0, 0.5)
        
        # Convert score to binary classification
        placement.append(1 if score >= 6.5 else 0)
    
    data['placed'] = placement
    
    return pd.DataFrame(data)

def train_model():
    """Train the placement prediction model"""
    print("Training the placement prediction model...")
    
    # Generate data
    df = create_dummy_data()
    
    # Prepare features and target
    features = ['cgpa', 'python_skill', 'java_skill', 'cpp_skill', 
               'data_science_skill', 'ai_ml_skill', 'sql_skill', 
               'internships', 'certifications', 'projects']
    
    X = df[features]
    y = df['placed']
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # Train Random Forest model
    model = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42
    )
    
    model.fit(X_train, y_train)
    
    # Evaluate model
    train_pred = model.predict(X_train)
    test_pred = model.predict(X_test)
    
    train_accuracy = accuracy_score(y_train, train_pred)
    test_accuracy = accuracy_score(y_test, test_pred)
    
    print(f"Training Accuracy: {train_accuracy:.4f}")
    print(f"Testing Accuracy: {test_accuracy:.4f}")
    print("\nClassification Report:")
    print(classification_report(y_test, test_pred))
    
    # Save the model
    with open('placement_model.pkl', 'wb') as file:
        pickle.dump(model, file)
    
    print("Model saved as 'placement_model.pkl'")
    
    # Save model accuracy for display in app
    model_info = {
        'train_accuracy': train_accuracy,
        'test_accuracy': test_accuracy,
        'feature_names': features
    }
    
    with open('model_info.pkl', 'wb') as file:
        pickle.dump(model_info, file)
    
    return model, model_info

def setup_database():
    """Setup SQLite database for storing student predictions"""
    print("Setting up SQLite database...")
    
    conn = sqlite3.connect('student_predictions.db')
    cursor = conn.cursor()
    
    # Create table for storing student predictions
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS predictions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            roll_number TEXT NOT NULL,
            cgpa REAL NOT NULL,
            skills TEXT NOT NULL,
            internships INTEGER NOT NULL,
            certifications INTEGER NOT NULL,
            projects INTEGER NOT NULL,
            prediction INTEGER NOT NULL,
            probability REAL NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()
    print("Database setup complete!")

if __name__ == "__main__":
    print("=== Student Placement Predictor - Setup ===")
    
    # Setup database
    setup_database()
    
    # Train and save model
    model, model_info = train_model()
    
    print("\n=== Setup Complete! ===")
    print("Now run: streamlit run app.py")