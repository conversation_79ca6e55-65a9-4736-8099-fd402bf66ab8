# Student Placement Predictor

A comprehensive web application that predicts student placement eligibility using Machine Learning, built with Streamlit and Python.

## Features

- 🎯 **AI-Powered Predictions**: Uses Random Forest algorithm to predict placement eligibility
- 📊 **Interactive UI**: Clean and attractive Streamlit interface
- 💾 **Data Persistence**: SQLite database to store prediction history
- 📈 **Visual Analytics**: Charts and graphs showing prediction probabilities
- 🔧 **Model Performance**: Displays training and testing accuracy
- 📱 **Responsive Design**: Works well on different screen sizes

## Prerequisites

- Python 3.7 or higher
- pip (Python package installer)

## Installation & Setup

### Step 1: Install Required Packages
```bash
pip install -r requirements.txt
```

### Step 2: Generate Training Data and Train Model
```bash
python generate_data.py
```
This will:
- Generate 1000 dummy student records for training
- Train a Random Forest model
- Save the trained model as `placement_model.pkl`
- Set up the SQLite database

### Step 3: Run the Streamlit Application
```bash
streamlit run app.py
```

### Step 4: Open in Browser
The app will automatically open in your default browser at `http://localhost:8501`

## How to Use

1. **Enter Personal Information**:
   - Full Name
   - Roll Number
   - CGPA (5.0 - 10.0)

2. **Add Experience Details**:
   - Number of Internships
   - Certifications (Yes/No)
   - Number of Projects

3. **Select Technical Skills**:
   - Python, Java, C++
   - Data Science, AI/ML, SQL
   - Web Development, Mobile Development, DevOps

4. **Get Prediction**:
   - Click "Predict Placement" button
   - View your eligibility result
   - See prediction confidence percentage
   - Get personalized recommendations

## Model Details

- **Algorithm**: Random Forest Classifier
- **Features**: 10 input features (CGPA, skills, experience)
- **Training Data**: 1000 synthetic student records
- **Accuracy**: Typically achieves 85-90% accuracy
- **Output**: Binary classification (Eligible/Not Eligible)

## Database Schema

The SQLite database stores:
- Student personal information
- Academic and experience details
- Prediction results and probabilities
- Timestamps for tracking

## File Structure

```
student-placement-predictor/
├── app.py                    # Main Streamlit application
├── generate_data.py          # Data generation and model training
├── requirements.txt          # Python dependencies
├── README.md                # This file
├── placement_model.pkl      # Trained ML model (generated)
├── model_info.pkl          # Model metadata (generated)
└── student_predictions.db  # SQLite database (generated)
```

## Customization

You can customize the application by:

1. **Adding More Features**: Modify `generate_data.py` to include additional student attributes
2. **Changing the Model**: Replace Random Forest with other algorithms in `generate_data.py`
3. **Updating UI**: Modify the CSS styles in `app.py` for different themes
4. **Extending Skills**: Add more technical skills in the skills selection list

## Troubleshooting

### Common Issues:

1. **ModuleNotFoundError**: Make sure all packages are installed with `pip install -r requirements.txt`

2. **Model not found error**: Run `python generate_data.py` before starting the Streamlit app

3. **Database errors**: Delete `student_predictions.db` and re-run `generate_data.py`

4. **Port already in use**: Use `streamlit run app.py --server.port 8502` to use a different port

## Technical Stack

- **Frontend**: Streamlit
- **Backend**: Python
- **Machine Learning**: Scikit-learn (Random Forest)
- **Database**: SQLite
- **Visualization**: Plotly
- **Data Processing**: Pandas, NumPy

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

---

**Note**: This application generates predictions based on historical patterns and should be used as guidance only. Actual placement outcomes depend on many factors including market conditions, company requirements, and interview performance.