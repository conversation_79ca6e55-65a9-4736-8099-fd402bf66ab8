#!/usr/bin/env python3
"""
Student Placement Predictor - Desktop Application
Built with tkinter for cross-platform desktop usage

Instructions to run:
1. Open terminal in VS Code (Terminal -> New Terminal)
2. Install requirements: pip install -r requirements.txt
3. Run data generation first: python generate_data.py
4. Run the desktop app: python desktop_app.py

Features:
- Native desktop GUI using tkinter
- Same ML prediction functionality as web version
- SQLite database integration
- Clean, modern interface design
- Real-time prediction results
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import pandas as pd
import numpy as np
import pickle
import sqlite3
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import seaborn as sns

class PlacementPredictorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("🎓 Student Placement Predictor")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # Load model and setup
        self.load_model()
        self.setup_styles()
        self.create_widgets()
        
    def load_model(self):
        """Load the trained ML model and model info"""
        try:
            with open('placement_model.pkl', 'rb') as file:
                self.model = pickle.load(file)
            
            with open('model_info.pkl', 'rb') as file:
                self.model_info = pickle.load(file)
                
        except FileNotFoundError:
            messagebox.showerror("Error", 
                               "Model files not found!\nPlease run 'python generate_data.py' first.")
            self.root.quit()
    
    def setup_styles(self):
        """Configure ttk styles for better appearance"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure custom styles
        style.configure('Title.TLabel', 
                       font=('Arial', 20, 'bold'),
                       background='#f0f0f0',
                       foreground='#1f77b4')
        
        style.configure('Heading.TLabel',
                       font=('Arial', 12, 'bold'),
                       background='#f0f0f0',
                       foreground='#333333')
        
        style.configure('Info.TLabel',
                       font=('Arial', 10),
                       background='#f0f0f0',
                       foreground='#666666')
        
        style.configure('Predict.TButton',
                       font=('Arial', 12, 'bold'),
                       padding=(20, 10))
    
    def create_widgets(self):
        """Create and arrange all GUI widgets"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, 
                               text="🎓 Student Placement Predictor",
                               style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Left panel - Input form
        self.create_input_panel(main_frame)
        
        # Right panel - Results and info
        self.create_results_panel(main_frame)
        
        # Bottom panel - History
        self.create_history_panel(main_frame)
    
    def create_input_panel(self, parent):
        """Create the input form panel"""
        input_frame = ttk.LabelFrame(parent, text="📝 Student Details", padding="15")
        input_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # Personal Information
        ttk.Label(input_frame, text="Personal Information", 
                 style='Heading.TLabel').grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        
        # Name
        ttk.Label(input_frame, text="👤 Full Name:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.name_var, width=25).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # Roll Number
        ttk.Label(input_frame, text="🆔 Roll Number:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.roll_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.roll_var, width=25).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # CGPA
        ttk.Label(input_frame, text="📊 CGPA:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.cgpa_var = tk.DoubleVar(value=7.5)
        cgpa_scale = ttk.Scale(input_frame, from_=5.0, to=10.0, 
                              variable=self.cgpa_var, orient=tk.HORIZONTAL)
        cgpa_scale.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # CGPA display
        self.cgpa_label = ttk.Label(input_frame, text="7.5")
        self.cgpa_label.grid(row=4, column=1, sticky=tk.W)
        cgpa_scale.configure(command=self.update_cgpa_label)
        
        # Academic Details
        ttk.Label(input_frame, text="Academic & Experience", 
                 style='Heading.TLabel').grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=(20, 10))
        
        # Internships
        ttk.Label(input_frame, text="💼 Internships:").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.internships_var = tk.IntVar()
        ttk.Spinbox(input_frame, from_=0, to=10, textvariable=self.internships_var, 
                   width=23).grid(row=6, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # Projects
        ttk.Label(input_frame, text="🚀 Projects:").grid(row=7, column=0, sticky=tk.W, pady=5)
        self.projects_var = tk.IntVar()
        ttk.Spinbox(input_frame, from_=0, to=20, textvariable=self.projects_var, 
                   width=23).grid(row=7, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # Certifications
        ttk.Label(input_frame, text="📜 Certifications:").grid(row=8, column=0, sticky=tk.W, pady=5)
        self.cert_var = tk.BooleanVar()
        ttk.Checkbutton(input_frame, text="Have Certifications", 
                       variable=self.cert_var).grid(row=8, column=1, sticky=tk.W, pady=5)
        
        # Skills
        ttk.Label(input_frame, text="💻 Technical Skills", 
                 style='Heading.TLabel').grid(row=9, column=0, columnspan=2, sticky=tk.W, pady=(20, 10))
        
        # Skills checkboxes
        self.skills_vars = {}
        skills_list = ['Python', 'Java', 'C++', 'Data Science', 'AI/ML', 'SQL']
        
        for i, skill in enumerate(skills_list):
            self.skills_vars[skill] = tk.BooleanVar()
            ttk.Checkbutton(input_frame, text=skill, 
                           variable=self.skills_vars[skill]).grid(
                               row=10 + i//2, column=i%2, sticky=tk.W, pady=2)
        
        # Predict button
        predict_btn = ttk.Button(input_frame, text="🔮 Predict Placement", 
                                style='Predict.TButton',
                                command=self.predict_placement)
        predict_btn.grid(row=13, column=0, columnspan=2, pady=20)
        
        # Configure column weights
        input_frame.columnconfigure(1, weight=1)
    
    def create_results_panel(self, parent):
        """Create the results and model info panel"""
        results_frame = ttk.LabelFrame(parent, text="📊 Results & Model Info", padding="15")
        results_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        
        # Model Information
        ttk.Label(results_frame, text="Model Performance", 
                 style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        
        model_info_text = f"""Algorithm: Random Forest
Training Accuracy: {self.model_info['train_accuracy']:.1%}
Testing Accuracy: {self.model_info['test_accuracy']:.1%}
Features: {len(self.model_info['feature_names'])}"""
        
        ttk.Label(results_frame, text=model_info_text, 
                 style='Info.TLabel').grid(row=1, column=0, sticky=tk.W, pady=(0, 20))
        
        # Prediction Results Area
        ttk.Label(results_frame, text="Prediction Results", 
                 style='Heading.TLabel').grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        
        # Results text area
        self.results_text = scrolledtext.ScrolledText(results_frame, 
                                                     width=40, height=15,
                                                     wrap=tk.WORD)
        self.results_text.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Initial message
        self.results_text.insert(tk.END, "Enter student details and click 'Predict Placement' to see results.\n\n")
        self.results_text.insert(tk.END, "The prediction will show:\n")
        self.results_text.insert(tk.END, "• Placement eligibility\n")
        self.results_text.insert(tk.END, "• Confidence percentage\n")
        self.results_text.insert(tk.END, "• Personalized recommendations\n")
        
        # Configure column weights
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(3, weight=1)
    
    def create_history_panel(self, parent):
        """Create the prediction history panel"""
        history_frame = ttk.LabelFrame(parent, text="📚 Recent Predictions", padding="15")
        history_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(20, 0))
        
        # History treeview
        columns = ('Name', 'Roll', 'CGPA', 'Prediction', 'Confidence', 'Timestamp')
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=6)
        
        # Configure columns
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=120)
        
        # Scrollbar for treeview
        scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)
        
        # Grid treeview and scrollbar
        self.history_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Configure weights
        history_frame.columnconfigure(0, weight=1)
        history_frame.rowconfigure(0, weight=1)
        
        # Load initial history
        self.load_history()
    
    def update_cgpa_label(self, value):
        """Update CGPA display label"""
        self.cgpa_label.config(text=f"{float(value):.1f}")
    
    def prepare_features(self, cgpa, skills, internships, certifications, projects):
        """Prepare features for model prediction"""
        features = {
            'cgpa': cgpa,
            'python_skill': 1 if skills.get('Python', False) else 0,
            'java_skill': 1 if skills.get('Java', False) else 0,
            'cpp_skill': 1 if skills.get('C++', False) else 0,
            'data_science_skill': 1 if skills.get('Data Science', False) else 0,
            'ai_ml_skill': 1 if skills.get('AI/ML', False) else 0,
            'sql_skill': 1 if skills.get('SQL', False) else 0,
            'internships': internships,
            'certifications': 1 if certifications else 0,
            'projects': projects
        }
        
        return list(features.values())
    
    def predict_placement(self):
        """Handle placement prediction"""
        # Validate inputs
        if not self.name_var.get().strip() or not self.roll_var.get().strip():
            messagebox.showerror("Error", "Please fill in Name and Roll Number!")
            return
        
        # Get input values
        name = self.name_var.get().strip()
        roll_number = self.roll_var.get().strip()
        cgpa = self.cgpa_var.get()
        internships = self.internships_var.get()
        projects = self.projects_var.get()
        certifications = self.cert_var.get()
        
        # Get selected skills
        skills = {}
        for skill, var in self.skills_vars.items():
            skills[skill] = var.get()
        
        # Prepare features
        features = self.prepare_features(cgpa, skills, internships, certifications, projects)
        
        # Make prediction
        prediction = self.model.predict([features])[0]
        probability = self.model.predict_proba([features])[0][1]
        
        # Display results
        self.display_results(name, roll_number, prediction, probability, cgpa, 
                           skills, internships, certifications, projects)
        
        # Save to database
        self.save_prediction(name, roll_number, cgpa, skills, internships, 
                           certifications, projects, prediction, probability)
        
        # Refresh history
        self.load_history()
    
    def display_results(self, name, roll_number, prediction, probability, cgpa, 
                       skills, internships, certifications, projects):
        """Display prediction results in the results panel"""
        self.results_text.delete(1.0, tk.END)
        
        # Header
        self.results_text.insert(tk.END, f"🎓 PREDICTION RESULTS\n")
        self.results_text.insert(tk.END, "="*40 + "\n\n")
        
        # Student info
        self.results_text.insert(tk.END, f"Student: {name}\n")
        self.results_text.insert(tk.END, f"Roll Number: {roll_number}\n\n")
        
        # Prediction result
        if prediction == 1:
            self.results_text.insert(tk.END, "🎉 RESULT: ELIGIBLE FOR PLACEMENT!\n")
            self.results_text.insert(tk.END, f"📊 Confidence: {probability:.1%}\n\n")
            self.results_text.insert(tk.END, "✅ Great job! Your profile looks strong for campus placements.\n\n")
        else:
            self.results_text.insert(tk.END, "📈 RESULT: NEEDS IMPROVEMENT\n")
            self.results_text.insert(tk.END, f"📊 Confidence: {(1-probability):.1%}\n\n")
            self.results_text.insert(tk.END, "💡 Consider enhancing your profile with the recommendations below.\n\n")
        
        # Profile summary
        self.results_text.insert(tk.END, "📋 PROFILE SUMMARY:\n")
        self.results_text.insert(tk.END, f"• CGPA: {cgpa:.1f}\n")
        self.results_text.insert(tk.END, f"• Internships: {internships}\n")
        self.results_text.insert(tk.END, f"• Projects: {projects}\n")
        self.results_text.insert(tk.END, f"• Certifications: {'Yes' if certifications else 'No'}\n")
        
        selected_skills = [skill for skill, selected in skills.items() if selected]
        self.results_text.insert(tk.END, f"• Skills: {', '.join(selected_skills) if selected_skills else 'None selected'}\n\n")
        
        # Recommendations
        if prediction == 0:
            self.results_text.insert(tk.END, "💡 RECOMMENDATIONS:\n")
            recommendations = []
            
            if cgpa < 7.0:
                recommendations.append(f"🔹 Focus on improving CGPA (current: {cgpa:.1f})")
            if internships < 2:
                recommendations.append(f"🔹 Gain more internship experience (current: {internships})")
            if len(selected_skills) < 3:
                recommendations.append(f"🔹 Learn more technical skills (current: {len(selected_skills)})")
            if projects < 3:
                recommendations.append(f"🔹 Work on more projects (current: {projects})")
            if not certifications:
                recommendations.append("🔹 Consider getting relevant certifications")
            
            for rec in recommendations:
                self.results_text.insert(tk.END, rec + "\n")
        else:
            self.results_text.insert(tk.END, "🌟 EXCELLENT PROFILE!\n")
            self.results_text.insert(tk.END, "Keep up the great work and continue building your skills!\n")
    
    def save_prediction(self, name, roll_number, cgpa, skills, internships, 
                       certifications, projects, prediction, probability):
        """Save prediction to SQLite database"""
        try:
            conn = sqlite3.connect('student_predictions.db')
            cursor = conn.cursor()
            
            selected_skills = [skill for skill, selected in skills.items() if selected]
            skills_str = ', '.join(selected_skills)
            
            cursor.execute('''
                INSERT INTO predictions (name, roll_number, cgpa, skills, internships, 
                                       certifications, projects, prediction, probability)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (name, roll_number, cgpa, skills_str, internships, 
                  certifications, projects, prediction, probability))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            messagebox.showerror("Database Error", f"Error saving prediction: {e}")
    
    def load_history(self):
        """Load and display prediction history"""
        try:
            # Clear existing items
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)
            
            # Load from database
            conn = sqlite3.connect('student_predictions.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT name, roll_number, cgpa, prediction, probability, timestamp
                FROM predictions 
                ORDER BY timestamp DESC 
                LIMIT 10
            ''')
            
            rows = cursor.fetchall()
            conn.close()
            
            # Insert into treeview
            for row in rows:
                name, roll_number, cgpa, prediction, probability, timestamp = row
                prediction_text = "✅ Eligible" if prediction == 1 else "❌ Not Eligible"
                probability_text = f"{probability:.1%}"
                
                self.history_tree.insert('', 'end', values=(
                    name, roll_number, f"{cgpa:.1f}", 
                    prediction_text, probability_text, timestamp
                ))
                
        except Exception as e:
            print(f"Error loading history: {e}")

def main():
    """Main function to run the desktop application"""
    root = tk.Tk()
    app = PlacementPredictorApp(root)
    
    # Center the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()